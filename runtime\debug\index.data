a:52:{s:13:"6877f447b7564";a:13:{s:3:"tag";s:13:"6877f447b7564";s:3:"url";s:48:"http://silver/backend/print-invoice/view?id=1519";s:4:"ajax";i:1;s:6:"method";s:3:"GET";s:2:"ip";s:9:"127.0.0.1";s:4:"time";d:1752691783.629661;s:10:"statusCode";i:200;s:8:"sqlCount";i:22;s:21:"excessiveCallersCount";i:0;s:9:"mailCount";i:0;s:9:"mailFiles";a:0:{}s:10:"peakMemory";i:10022968;s:14:"processingTime";d:0.20590996742248535;}s:13:"687846a37fd89";a:13:{s:3:"tag";s:13:"687846a37fd89";s:3:"url";s:35:"http://silver/backend/invoice/index";s:4:"ajax";i:0;s:6:"method";s:3:"GET";s:2:"ip";s:9:"127.0.0.1";s:4:"time";d:1752712866.743936;s:10:"statusCode";i:302;s:8:"sqlCount";i:4;s:21:"excessiveCallersCount";i:0;s:9:"mailCount";i:0;s:9:"mailFiles";a:0:{}s:10:"peakMemory";i:8688456;s:14:"processingTime";d:0.7412261962890625;}s:13:"687846a3aaa6c";a:13:{s:3:"tag";s:13:"687846a3aaa6c";s:3:"url";s:19:"http://silver/login";s:4:"ajax";i:0;s:6:"method";s:3:"GET";s:2:"ip";s:9:"127.0.0.1";s:4:"time";d:1752712867.612931;s:10:"statusCode";i:200;s:8:"sqlCount";i:0;s:21:"excessiveCallersCount";i:0;s:9:"mailCount";i:0;s:9:"mailFiles";a:0:{}s:10:"peakMemory";i:7963544;s:14:"processingTime";d:0.14088988304138184;}s:13:"687846baaeada";a:13:{s:3:"tag";s:13:"687846baaeada";s:3:"url";s:19:"http://silver/login";s:4:"ajax";i:0;s:6:"method";s:4:"POST";s:2:"ip";s:9:"127.0.0.1";s:4:"time";d:1752712890.672214;s:10:"statusCode";i:200;s:8:"sqlCount";i:3;s:21:"excessiveCallersCount";i:0;s:9:"mailCount";i:0;s:9:"mailFiles";a:0:{}s:10:"peakMemory";i:9795544;s:14:"processingTime";d:0.6670420169830322;}s:13:"687846bb57526";a:13:{s:3:"tag";s:13:"687846bb57526";s:3:"url";s:19:"http://silver/login";s:4:"ajax";i:0;s:6:"method";s:4:"POST";s:2:"ip";s:9:"127.0.0.1";s:4:"time";d:1752712890.877536;s:10:"statusCode";i:200;s:8:"sqlCount";i:3;s:21:"excessiveCallersCount";i:0;s:9:"mailCount";i:0;s:9:"mailFiles";a:0:{}s:10:"peakMemory";i:9795648;s:14:"processingTime";d:1.0816540718078613;}s:13:"687846bdef99c";a:13:{s:3:"tag";s:13:"687846bdef99c";s:3:"url";s:19:"http://silver/login";s:4:"ajax";i:0;s:6:"method";s:4:"POST";s:2:"ip";s:9:"127.0.0.1";s:4:"time";d:1752712893.931397;s:10:"statusCode";i:200;s:8:"sqlCount";i:3;s:21:"excessiveCallersCount";i:0;s:9:"mailCount";i:0;s:9:"mailFiles";a:0:{}s:10:"peakMemory";i:9795544;s:14:"processingTime";d:0.6551470756530762;}s:13:"687846be938a5";a:13:{s:3:"tag";s:13:"687846be938a5";s:3:"url";s:19:"http://silver/login";s:4:"ajax";i:0;s:6:"method";s:4:"POST";s:2:"ip";s:9:"127.0.0.1";s:4:"time";d:1752712894.1464;s:10:"statusCode";i:200;s:8:"sqlCount";i:3;s:21:"excessiveCallersCount";i:0;s:9:"mailCount";i:0;s:9:"mailFiles";a:0:{}s:10:"peakMemory";i:9795648;s:14:"processingTime";d:1.075913906097412;}s:13:"687846c82e33f";a:13:{s:3:"tag";s:13:"687846c82e33f";s:3:"url";s:19:"http://silver/login";s:4:"ajax";i:0;s:6:"method";s:4:"POST";s:2:"ip";s:9:"127.0.0.1";s:4:"time";d:1752712904.147615;s:10:"statusCode";i:302;s:8:"sqlCount";i:3;s:21:"excessiveCallersCount";i:0;s:9:"mailCount";i:0;s:9:"mailFiles";a:0:{}s:10:"peakMemory";i:8644896;s:14:"processingTime";d:0.7530269622802734;}s:13:"687846c8de954";a:13:{s:3:"tag";s:13:"687846c8de954";s:3:"url";s:19:"http://silver/login";s:4:"ajax";i:0;s:6:"method";s:4:"POST";s:2:"ip";s:9:"127.0.0.1";s:4:"time";d:1752712904.353623;s:10:"statusCode";i:302;s:8:"sqlCount";i:3;s:21:"excessiveCallersCount";i:0;s:9:"mailCount";i:0;s:9:"mailFiles";a:0:{}s:10:"peakMemory";i:8645000;s:14:"processingTime";d:1.1399338245391846;}s:13:"687846c9a7451";a:13:{s:3:"tag";s:13:"687846c9a7451";s:3:"url";s:36:"http://silver/backend/position/index";s:4:"ajax";i:0;s:6:"method";s:3:"GET";s:2:"ip";s:9:"127.0.0.1";s:4:"time";d:1752712905.535614;s:10:"statusCode";i:200;s:8:"sqlCount";i:8;s:21:"excessiveCallersCount";i:0;s:9:"mailCount";i:0;s:9:"mailFiles";a:0:{}s:10:"peakMemory";i:10405960;s:14:"processingTime";d:0.18439292907714844;}s:13:"687846cd39e03";a:13:{s:3:"tag";s:13:"687846cd39e03";s:3:"url";s:35:"http://silver/backend/invoice/index";s:4:"ajax";i:0;s:6:"method";s:3:"GET";s:2:"ip";s:9:"127.0.0.1";s:4:"time";d:1752712909.120986;s:10:"statusCode";i:200;s:8:"sqlCount";i:1024;s:21:"excessiveCallersCount";i:0;s:9:"mailCount";i:0;s:9:"mailFiles";a:0:{}s:10:"peakMemory";i:33591872;s:14:"processingTime";d:1.2077529430389404;}s:13:"687846d0e4ada";a:13:{s:3:"tag";s:13:"687846d0e4ada";s:3:"url";s:42:"http://silver/backend/invoice/view?id=1519";s:4:"ajax";i:1;s:6:"method";s:3:"GET";s:2:"ip";s:9:"127.0.0.1";s:4:"time";d:1752712912.777191;s:10:"statusCode";i:200;s:8:"sqlCount";i:22;s:21:"excessiveCallersCount";i:0;s:9:"mailCount";i:0;s:9:"mailFiles";a:0:{}s:10:"peakMemory";i:10305792;s:14:"processingTime";d:0.2581310272216797;}s:13:"687846d6362e6";a:13:{s:3:"tag";s:13:"687846d6362e6";s:3:"url";s:48:"http://silver/backend/print-invoice/view?id=1519";s:4:"ajax";i:1;s:6:"method";s:3:"GET";s:2:"ip";s:9:"127.0.0.1";s:4:"time";d:1752712918.059957;s:10:"statusCode";i:200;s:8:"sqlCount";i:23;s:21:"excessiveCallersCount";i:0;s:9:"mailCount";i:0;s:9:"mailFiles";a:0:{}s:10:"peakMemory";i:10029336;s:14:"processingTime";d:0.22831106185913086;}s:13:"687846dd0049c";a:13:{s:3:"tag";s:13:"687846dd0049c";s:3:"url";s:35:"http://silver/backend/invoice/index";s:4:"ajax";i:0;s:6:"method";s:3:"GET";s:2:"ip";s:9:"127.0.0.1";s:4:"time";d:1752712924.87848;s:10:"statusCode";i:200;s:8:"sqlCount";i:1024;s:21:"excessiveCallersCount";i:0;s:9:"mailCount";i:0;s:9:"mailFiles";a:0:{}s:10:"peakMemory";i:33593096;s:14:"processingTime";d:1.6555278301239014;}s:13:"687846e13b3b0";a:13:{s:3:"tag";s:13:"687846e13b3b0";s:3:"url";s:35:"http://silver/backend/client/create";s:4:"ajax";i:1;s:6:"method";s:3:"GET";s:2:"ip";s:9:"127.0.0.1";s:4:"time";d:1752712929.037245;s:10:"statusCode";i:200;s:8:"sqlCount";i:8;s:21:"excessiveCallersCount";i:0;s:9:"mailCount";i:0;s:9:"mailFiles";a:0:{}s:10:"peakMemory";i:10074552;s:14:"processingTime";d:0.2414379119873047;}s:13:"687846e5ee8f8";a:13:{s:3:"tag";s:13:"687846e5ee8f8";s:3:"url";s:42:"http://silver/backend/invoice/view?id=1519";s:4:"ajax";i:1;s:6:"method";s:3:"GET";s:2:"ip";s:9:"127.0.0.1";s:4:"time";d:1752712933.707157;s:10:"statusCode";i:200;s:8:"sqlCount";i:22;s:21:"excessiveCallersCount";i:0;s:9:"mailCount";i:0;s:9:"mailFiles";a:0:{}s:10:"peakMemory";i:10307016;s:14:"processingTime";d:0.35765504837036133;}s:13:"687846e73bc9f";a:13:{s:3:"tag";s:13:"687846e73bc9f";s:3:"url";s:48:"http://silver/backend/print-invoice/view?id=1519";s:4:"ajax";i:1;s:6:"method";s:3:"GET";s:2:"ip";s:9:"127.0.0.1";s:4:"time";d:1752712935.01746;s:10:"statusCode";i:200;s:8:"sqlCount";i:23;s:21:"excessiveCallersCount";i:0;s:9:"mailCount";i:0;s:9:"mailFiles";a:0:{}s:10:"peakMemory";i:10029336;s:14:"processingTime";d:0.3157200813293457;}s:13:"687847a83d108";a:13:{s:3:"tag";s:13:"687847a83d108";s:3:"url";s:35:"http://silver/backend/invoice/index";s:4:"ajax";i:0;s:6:"method";s:3:"GET";s:2:"ip";s:9:"127.0.0.1";s:4:"time";d:1752713127.874853;s:10:"statusCode";i:200;s:8:"sqlCount";i:1024;s:21:"excessiveCallersCount";i:0;s:9:"mailCount";i:0;s:9:"mailFiles";a:0:{}s:10:"peakMemory";i:33593096;s:14:"processingTime";d:2.194597005844116;}s:13:"687847b4ba3bc";a:13:{s:3:"tag";s:13:"687847b4ba3bc";s:3:"url";s:42:"http://silver/backend/invoice/view?id=1519";s:4:"ajax";i:1;s:6:"method";s:3:"GET";s:2:"ip";s:9:"127.0.0.1";s:4:"time";d:1752713140.365405;s:10:"statusCode";i:200;s:8:"sqlCount";i:22;s:21:"excessiveCallersCount";i:0;s:9:"mailCount";i:0;s:9:"mailFiles";a:0:{}s:10:"peakMemory";i:10307016;s:14:"processingTime";d:0.5036308765411377;}s:13:"687847b80fb70";a:13:{s:3:"tag";s:13:"687847b80fb70";s:3:"url";s:48:"http://silver/backend/print-invoice/view?id=1519";s:4:"ajax";i:1;s:6:"method";s:3:"GET";s:2:"ip";s:9:"127.0.0.1";s:4:"time";d:1752713143.826388;s:10:"statusCode";i:200;s:8:"sqlCount";i:23;s:21:"excessiveCallersCount";i:0;s:9:"mailCount";i:0;s:9:"mailFiles";a:0:{}s:10:"peakMemory";i:10033024;s:14:"processingTime";d:0.3422129154205322;}s:13:"687847c3d0205";a:13:{s:3:"tag";s:13:"687847c3d0205";s:3:"url";s:53:"http://silver/backend/print-invoice/save-print-number";s:4:"ajax";i:1;s:6:"method";s:4:"POST";s:2:"ip";s:9:"127.0.0.1";s:4:"time";d:1752713155.608642;s:10:"statusCode";i:200;s:8:"sqlCount";i:10;s:21:"excessiveCallersCount";i:0;s:9:"mailCount";i:0;s:9:"mailFiles";a:0:{}s:10:"peakMemory";i:9107744;s:14:"processingTime";d:0.2753901481628418;}s:13:"6878483c3a05f";a:13:{s:3:"tag";s:13:"6878483c3a05f";s:3:"url";s:35:"http://silver/backend/invoice/index";s:4:"ajax";i:0;s:6:"method";s:3:"GET";s:2:"ip";s:9:"127.0.0.1";s:4:"time";d:1752713276.003978;s:10:"statusCode";i:200;s:8:"sqlCount";i:1024;s:21:"excessiveCallersCount";i:0;s:9:"mailCount";i:0;s:9:"mailFiles";a:0:{}s:10:"peakMemory";i:33572600;s:14:"processingTime";d:1.758126974105835;}s:13:"687897127467a";a:13:{s:3:"tag";s:13:"687897127467a";s:3:"url";s:35:"http://silver/backend/invoice/index";s:4:"ajax";i:0;s:6:"method";s:3:"GET";s:2:"ip";s:9:"127.0.0.1";s:4:"time";d:1752733458.034727;s:10:"statusCode";i:302;s:8:"sqlCount";i:4;s:21:"excessiveCallersCount";i:0;s:9:"mailCount";i:0;s:9:"mailFiles";a:0:{}s:10:"peakMemory";i:8688456;s:14:"processingTime";d:0.42353200912475586;}s:13:"687897128f123";a:13:{s:3:"tag";s:13:"687897128f123";s:3:"url";s:19:"http://silver/login";s:4:"ajax";i:0;s:6:"method";s:3:"GET";s:2:"ip";s:9:"127.0.0.1";s:4:"time";d:1752733458.521093;s:10:"statusCode";i:200;s:8:"sqlCount";i:0;s:21:"excessiveCallersCount";i:0;s:9:"mailCount";i:0;s:9:"mailFiles";a:0:{}s:10:"peakMemory";i:7963544;s:14:"processingTime";d:0.12987613677978516;}s:13:"687897189eabd";a:13:{s:3:"tag";s:13:"687897189eabd";s:3:"url";s:19:"http://silver/login";s:4:"ajax";i:0;s:6:"method";s:4:"POST";s:2:"ip";s:9:"127.0.0.1";s:4:"time";d:1752733464.601836;s:10:"statusCode";i:302;s:8:"sqlCount";i:3;s:21:"excessiveCallersCount";i:0;s:9:"mailCount";i:0;s:9:"mailFiles";a:0:{}s:10:"peakMemory";i:8644896;s:14:"processingTime";d:0.7070169448852539;}s:13:"687897194e862";a:13:{s:3:"tag";s:13:"687897194e862";s:3:"url";s:19:"http://silver/login";s:4:"ajax";i:0;s:6:"method";s:4:"POST";s:2:"ip";s:9:"127.0.0.1";s:4:"time";d:1752733464.804586;s:10:"statusCode";i:302;s:8:"sqlCount";i:3;s:21:"excessiveCallersCount";i:0;s:9:"mailCount";i:0;s:9:"mailFiles";a:0:{}s:10:"peakMemory";i:8645000;s:14:"processingTime";d:1.06026291847229;}s:13:"6878971a1ba90";a:13:{s:3:"tag";s:13:"6878971a1ba90";s:3:"url";s:36:"http://silver/backend/position/index";s:4:"ajax";i:0;s:6:"method";s:3:"GET";s:2:"ip";s:9:"127.0.0.1";s:4:"time";d:1752733465.968408;s:10:"statusCode";i:200;s:8:"sqlCount";i:8;s:21:"excessiveCallersCount";i:0;s:9:"mailCount";i:0;s:9:"mailFiles";a:0:{}s:10:"peakMemory";i:10405960;s:14:"processingTime";d:0.20041394233703613;}s:13:"68789725c2ba1";a:13:{s:3:"tag";s:13:"68789725c2ba1";s:3:"url";s:20:"http://silver/logout";s:4:"ajax";i:0;s:6:"method";s:4:"POST";s:2:"ip";s:9:"127.0.0.1";s:4:"time";d:1752733477.642659;s:10:"statusCode";i:302;s:8:"sqlCount";i:3;s:21:"excessiveCallersCount";i:0;s:9:"mailCount";i:0;s:9:"mailFiles";a:0:{}s:10:"peakMemory";i:8621160;s:14:"processingTime";d:0.1640779972076416;}s:13:"68789725dbe93";a:13:{s:3:"tag";s:13:"68789725dbe93";s:3:"url";s:19:"http://silver/login";s:4:"ajax";i:0;s:6:"method";s:3:"GET";s:2:"ip";s:9:"127.0.0.1";s:4:"time";d:1752733477.831043;s:10:"statusCode";i:200;s:8:"sqlCount";i:0;s:21:"excessiveCallersCount";i:0;s:9:"mailCount";i:0;s:9:"mailFiles";a:0:{}s:10:"peakMemory";i:7881776;s:14:"processingTime";d:0.10031414031982422;}s:13:"6878972c87bc8";a:13:{s:3:"tag";s:13:"6878972c87bc8";s:3:"url";s:19:"http://silver/login";s:4:"ajax";i:0;s:6:"method";s:4:"POST";s:2:"ip";s:9:"127.0.0.1";s:4:"time";d:1752733484.486696;s:10:"statusCode";i:200;s:8:"sqlCount";i:3;s:21:"excessiveCallersCount";i:0;s:9:"mailCount";i:0;s:9:"mailFiles";a:0:{}s:10:"peakMemory";i:9795536;s:14:"processingTime";d:0.8548328876495361;}s:13:"6878972d5849b";a:13:{s:3:"tag";s:13:"6878972d5849b";s:3:"url";s:19:"http://silver/login";s:4:"ajax";i:0;s:6:"method";s:4:"POST";s:2:"ip";s:9:"127.0.0.1";s:4:"time";d:1752733484.693692;s:10:"statusCode";i:200;s:8:"sqlCount";i:3;s:21:"excessiveCallersCount";i:0;s:9:"mailCount";i:0;s:9:"mailFiles";a:0:{}s:10:"peakMemory";i:9795640;s:14:"processingTime";d:1.525501012802124;}s:13:"6878973301fd9";a:13:{s:3:"tag";s:13:"6878973301fd9";s:3:"url";s:19:"http://silver/login";s:4:"ajax";i:0;s:6:"method";s:4:"POST";s:2:"ip";s:9:"127.0.0.1";s:4:"time";d:1752733490.913607;s:10:"statusCode";i:302;s:8:"sqlCount";i:3;s:21:"excessiveCallersCount";i:0;s:9:"mailCount";i:0;s:9:"mailFiles";a:0:{}s:10:"peakMemory";i:8644880;s:14:"processingTime";d:1.4203851222991943;}i:6878973457453;a:13:{s:3:"tag";s:13:"6878973457453";s:3:"url";s:19:"http://silver/login";s:4:"ajax";i:0;s:6:"method";s:4:"POST";s:2:"ip";s:9:"127.0.0.1";s:4:"time";d:1752733491.116626;s:10:"statusCode";i:302;s:8:"sqlCount";i:3;s:21:"excessiveCallersCount";i:0;s:9:"mailCount";i:0;s:9:"mailFiles";a:0:{}s:10:"peakMemory";i:8644984;s:14:"processingTime";d:2.5615060329437256;}s:13:"68789735e546e";a:13:{s:3:"tag";s:13:"68789735e546e";s:3:"url";s:36:"http://silver/backend/position/index";s:4:"ajax";i:0;s:6:"method";s:3:"GET";s:2:"ip";s:9:"127.0.0.1";s:4:"time";d:1752733493.730817;s:10:"statusCode";i:302;s:8:"sqlCount";i:7;s:21:"excessiveCallersCount";i:0;s:9:"mailCount";i:0;s:9:"mailFiles";a:0:{}s:10:"peakMemory";i:8877872;s:14:"processingTime";d:0.22930192947387695;}s:13:"687897363131e";a:13:{s:3:"tag";s:13:"687897363131e";s:3:"url";s:41:"http://silver/backend/sales-invoice/index";s:4:"ajax";i:0;s:6:"method";s:3:"GET";s:2:"ip";s:9:"127.0.0.1";s:4:"time";d:1752733493.999157;s:10:"statusCode";i:200;s:8:"sqlCount";i:214;s:21:"excessiveCallersCount";i:0;s:9:"mailCount";i:0;s:9:"mailFiles";a:0:{}s:10:"peakMemory";i:13592304;s:14:"processingTime";d:0.6845791339874268;}s:13:"687897433fead";a:13:{s:3:"tag";s:13:"687897433fead";s:3:"url";s:42:"http://silver/backend/sales-invoice/create";s:4:"ajax";i:1;s:6:"method";s:3:"GET";s:2:"ip";s:9:"127.0.0.1";s:4:"time";d:1752733507.036424;s:10:"statusCode";i:200;s:8:"sqlCount";i:17;s:21:"excessiveCallersCount";i:0;s:9:"mailCount";i:0;s:9:"mailFiles";a:0:{}s:10:"peakMemory";i:11254320;s:14:"processingTime";d:0.34328389167785645;}i:6878974433662;a:13:{s:3:"tag";s:13:"6878974433662";s:3:"url";s:56:"http://silver/backend/sales-invoice/get-batch-quantities";s:4:"ajax";i:1;s:6:"method";s:4:"POST";s:2:"ip";s:9:"127.0.0.1";s:4:"time";d:1752733507.959324;s:10:"statusCode";i:200;s:8:"sqlCount";i:10;s:21:"excessiveCallersCount";i:0;s:9:"mailCount";i:0;s:9:"mailFiles";a:0:{}s:10:"peakMemory";i:9174352;s:14:"processingTime";d:0.2597489356994629;}s:13:"687897445e83f";a:13:{s:3:"tag";s:13:"687897445e83f";s:3:"url";s:56:"http://silver/backend/sales-invoice/get-batch-quantities";s:4:"ajax";i:1;s:6:"method";s:4:"POST";s:2:"ip";s:9:"127.0.0.1";s:4:"time";d:1752733507.961321;s:10:"statusCode";i:200;s:8:"sqlCount";i:10;s:21:"excessiveCallersCount";i:0;s:9:"mailCount";i:0;s:9:"mailFiles";a:0:{}s:10:"peakMemory";i:9174352;s:14:"processingTime";d:0.4654879570007324;}s:13:"687897449527a";a:13:{s:3:"tag";s:13:"687897449527a";s:3:"url";s:56:"http://silver/backend/sales-invoice/get-batch-quantities";s:4:"ajax";i:1;s:6:"method";s:4:"POST";s:2:"ip";s:9:"127.0.0.1";s:4:"time";d:1752733507.97049;s:10:"statusCode";i:200;s:8:"sqlCount";i:10;s:21:"excessiveCallersCount";i:0;s:9:"mailCount";i:0;s:9:"mailFiles";a:0:{}s:10:"peakMemory";i:9174352;s:14:"processingTime";d:0.6352298259735107;}s:13:"68789744bea29";a:13:{s:3:"tag";s:13:"68789744bea29";s:3:"url";s:56:"http://silver/backend/sales-invoice/get-batch-quantities";s:4:"ajax";i:1;s:6:"method";s:4:"POST";s:2:"ip";s:9:"127.0.0.1";s:4:"time";d:1752733507.963363;s:10:"statusCode";i:200;s:8:"sqlCount";i:10;s:21:"excessiveCallersCount";i:0;s:9:"mailCount";i:0;s:9:"mailFiles";a:0:{}s:10:"peakMemory";i:9174352;s:14:"processingTime";d:0.8186028003692627;}s:13:"68789744e8422";a:13:{s:3:"tag";s:13:"68789744e8422";s:3:"url";s:56:"http://silver/backend/sales-invoice/get-batch-quantities";s:4:"ajax";i:1;s:6:"method";s:4:"POST";s:2:"ip";s:9:"127.0.0.1";s:4:"time";d:1752733507.969362;s:10:"statusCode";i:200;s:8:"sqlCount";i:10;s:21:"excessiveCallersCount";i:0;s:9:"mailCount";i:0;s:9:"mailFiles";a:0:{}s:10:"peakMemory";i:9174352;s:14:"processingTime";d:0.9873268604278564;}s:13:"6878974522e6c";a:13:{s:3:"tag";s:13:"6878974522e6c";s:3:"url";s:56:"http://silver/backend/sales-invoice/get-batch-quantities";s:4:"ajax";i:1;s:6:"method";s:4:"POST";s:2:"ip";s:9:"127.0.0.1";s:4:"time";d:1752733507.966363;s:10:"statusCode";i:200;s:8:"sqlCount";i:10;s:21:"excessiveCallersCount";i:0;s:9:"mailCount";i:0;s:9:"mailFiles";a:0:{}s:10:"peakMemory";i:9174352;s:14:"processingTime";d:1.1435308456420898;}s:13:"687897454daf3";a:13:{s:3:"tag";s:13:"687897454daf3";s:3:"url";s:56:"http://silver/backend/sales-invoice/get-batch-quantities";s:4:"ajax";i:1;s:6:"method";s:4:"POST";s:2:"ip";s:9:"127.0.0.1";s:4:"time";d:1752733508.263244;s:10:"statusCode";i:200;s:8:"sqlCount";i:10;s:21:"excessiveCallersCount";i:0;s:9:"mailCount";i:0;s:9:"mailFiles";a:0:{}s:10:"peakMemory";i:9174352;s:14:"processingTime";d:1.094290018081665;}s:13:"687897457a625";a:13:{s:3:"tag";s:13:"687897457a625";s:3:"url";s:56:"http://silver/backend/sales-invoice/get-batch-quantities";s:4:"ajax";i:1;s:6:"method";s:4:"POST";s:2:"ip";s:9:"127.0.0.1";s:4:"time";d:1752733508.476259;s:10:"statusCode";i:200;s:8:"sqlCount";i:10;s:21:"excessiveCallersCount";i:0;s:9:"mailCount";i:0;s:9:"mailFiles";a:0:{}s:10:"peakMemory";i:9174352;s:14:"processingTime";d:1.0359020233154297;}s:13:"68789745a5c60";a:13:{s:3:"tag";s:13:"68789745a5c60";s:3:"url";s:56:"http://silver/backend/sales-invoice/get-batch-quantities";s:4:"ajax";i:1;s:6:"method";s:4:"POST";s:2:"ip";s:9:"127.0.0.1";s:4:"time";d:1752733508.67865;s:10:"statusCode";i:200;s:8:"sqlCount";i:10;s:21:"excessiveCallersCount";i:0;s:9:"mailCount";i:0;s:9:"mailFiles";a:0:{}s:10:"peakMemory";i:9174352;s:14:"processingTime";d:1.0385189056396484;}s:13:"68789745d9bd4";a:13:{s:3:"tag";s:13:"68789745d9bd4";s:3:"url";s:61:"http://silver/backend/sales-invoice/get-drivers?client_id=374";s:4:"ajax";i:1;s:6:"method";s:3:"GET";s:2:"ip";s:9:"127.0.0.1";s:4:"time";d:1752733509.318511;s:10:"statusCode";i:200;s:8:"sqlCount";i:10;s:21:"excessiveCallersCount";i:0;s:9:"mailCount";i:0;s:9:"mailFiles";a:0:{}s:10:"peakMemory";i:9160880;s:14:"processingTime";d:0.5966160297393799;}s:13:"687897460730e";a:13:{s:3:"tag";s:13:"687897460730e";s:3:"url";s:60:"http://silver/backend/sales-invoice/get-prices?client_id=374";s:4:"ajax";i:1;s:6:"method";s:3:"GET";s:2:"ip";s:9:"127.0.0.1";s:4:"time";d:1752733509.324941;s:10:"statusCode";i:200;s:8:"sqlCount";i:16;s:21:"excessiveCallersCount";i:0;s:9:"mailCount";i:0;s:9:"mailFiles";a:0:{}s:10:"peakMemory";i:9349480;s:14:"processingTime";d:0.7647781372070312;}s:13:"6878974b21808";a:13:{s:3:"tag";s:13:"6878974b21808";s:3:"url";s:42:"http://silver/backend/sales-invoice/create";s:4:"ajax";i:1;s:6:"method";s:4:"POST";s:2:"ip";s:9:"127.0.0.1";s:4:"time";d:1752733514.922046;s:10:"statusCode";i:200;s:8:"sqlCount";i:43;s:21:"excessiveCallersCount";i:0;s:9:"mailCount";i:0;s:9:"mailFiles";a:0:{}s:10:"peakMemory";i:10234040;s:14:"processingTime";d:0.46379685401916504;}s:13:"6878974c02f59";a:13:{s:3:"tag";s:13:"6878974c02f59";s:3:"url";s:41:"http://silver/backend/sales-invoice/index";s:4:"ajax";i:0;s:6:"method";s:3:"GET";s:2:"ip";s:9:"127.0.0.1";s:4:"time";d:1752733515.668189;s:10:"statusCode";i:200;s:8:"sqlCount";i:214;s:21:"excessiveCallersCount";i:0;s:9:"mailCount";i:0;s:9:"mailFiles";a:0:{}s:10:"peakMemory";i:13592352;s:14:"processingTime";d:0.9059948921203613;}s:13:"6878974f20d86";a:13:{s:3:"tag";s:13:"6878974f20d86";s:3:"url";s:48:"http://silver/backend/sales-invoice/view?id=1520";s:4:"ajax";i:1;s:6:"method";s:3:"GET";s:2:"ip";s:9:"127.0.0.1";s:4:"time";d:1752733518.901464;s:10:"statusCode";i:200;s:8:"sqlCount";i:21;s:21:"excessiveCallersCount";i:0;s:9:"mailCount";i:0;s:9:"mailFiles";a:0:{}s:10:"peakMemory";i:10277320;s:14:"processingTime";d:0.352614164352417;}s:13:"68789750b1b32";a:13:{s:3:"tag";s:13:"68789750b1b32";s:3:"url";s:40:"http://silver/backend/sales-invoice/view";s:4:"ajax";i:1;s:6:"method";s:3:"GET";s:2:"ip";s:9:"127.0.0.1";s:4:"time";d:1752733520.475642;s:10:"statusCode";i:400;s:8:"sqlCount";i:7;s:21:"excessiveCallersCount";i:0;s:9:"mailCount";i:0;s:9:"mailFiles";a:0:{}s:10:"peakMemory";i:10747576;s:14:"processingTime";d:1.205528974533081;}s:13:"68789751cdab1";a:13:{s:3:"tag";s:13:"68789751cdab1";s:3:"url";s:48:"http://silver/backend/print-invoice/view?id=1520";s:4:"ajax";i:1;s:6:"method";s:3:"GET";s:2:"ip";s:9:"127.0.0.1";s:4:"time";d:1752733520.474642;s:10:"statusCode";i:200;s:8:"sqlCount";i:22;s:21:"excessiveCallersCount";i:0;s:9:"mailCount";i:0;s:9:"mailFiles";a:0:{}s:10:"peakMemory";i:10009336;s:14:"processingTime";d:1.4563369750976562;}}